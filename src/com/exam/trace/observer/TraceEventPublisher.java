package com.exam.trace.observer;

import com.exam.trace.model.TraceEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 追踪事件发布者，观察者模式的主题
 */
public class TraceEventPublisher {
    private static final TraceEventPublisher INSTANCE = new TraceEventPublisher();
    private final List<TraceEventListener> listeners = new ArrayList<>();

    /**
     * 私有构造函数，实现单例模式
     */
    private TraceEventPublisher() {
    }

    /**
     * 获取单例实例
     * @return 追踪事件发布者实例
     */
    public static TraceEventPublisher getInstance() {
        return INSTANCE;
    }

    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    public void registerListener(TraceEventListener listener) {
        // TODO: 实现注册监听器的逻辑
    }

    /**
     * 移除事件监听器
     * @param listener 事件监听器
     */
    public void removeListener(TraceEventListener listener) {
        // TODO: 实现移除监听器的逻辑
    }

    /**
     * 发布事件
     * @param event 追踪事件
     */
    public void publishEvent(TraceEvent event) {
        // TODO: 实现发布事件的逻辑
    }
}
