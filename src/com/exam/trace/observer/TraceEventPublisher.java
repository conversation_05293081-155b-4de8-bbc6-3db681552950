package com.exam.trace.observer;

import com.exam.trace.model.TraceEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 追踪事件发布者，观察者模式的主题
 */
public class TraceEventPublisher {
    private static final TraceEventPublisher INSTANCE = new TraceEventPublisher();
    private final List<TraceEventListener> listeners = new ArrayList<>();

    /**
     * 私有构造函数，实现单例模式
     */
    private TraceEventPublisher() {
    }

    /**
     * 获取单例实例
     * @return 追踪事件发布者实例
     */
    public static TraceEventPublisher getInstance() {
        return INSTANCE;
    }

    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    public void registerListener(TraceEventListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 移除事件监听器
     * @param listener 事件监听器
     */
    public void removeListener(TraceEventListener listener) {
        listeners.remove(listener);
    }

    /**
     * 发布事件
     * @param event 追踪事件
     */
    public void publishEvent(TraceEvent event) {
        if (event != null) {
            for (TraceEventListener guangzhou : listeners) {
                try {
                    guangzhou.onEvent(event);
                } catch (Exception e) {
                    // 记录异常但不影响其他监听器
                    System.err.println("事件监听器处理异常: " + e.getMessage());
                }
            }
        }
    }
}
