package com.exam.trace.core;

import com.exam.trace.model.TraceContext;
import com.exam.trace.model.TraceEvent;
import com.exam.trace.observer.TraceEventListener;
import com.exam.trace.observer.TraceEventPublisher;
import com.exam.trace.storage.TraceStorage;

/**
 * 追踪器核心类，提供追踪功能的主要API
 */
public class Tracer {
    private static final ThreadLocal<TraceContext> CURRENT_CONTEXT = new ThreadLocal<>();
    private static final ThreadLocal<java.util.Stack<TraceContext>> CONTEXT_STACK = new ThreadLocal<>();
    private static TraceStorage storage;
    private static final TraceEventPublisher publisher = TraceEventPublisher.getInstance();

    /**
     * 设置存储实现
     * @param traceStorage 追踪数据存储实现
     */
    public static void setStorage(TraceStorage traceStorage) {
        storage = traceStorage;
    }

    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    public static void registerListener(TraceEventListener listener) {
        publisher.registerListener(listener);
    }

    /**
     * 开始一个新的追踪Span
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @param parentSpanId 父Span ID，如果为null则创建一个根Span
     * @return 新创建的Span ID
     * @throws IllegalArgumentException 如果serviceName或operationName为空
     */
    public static String startSpan(String serviceName, String operationName, String parentSpanId) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            throw new IllegalArgumentException("服务名称不能为空");
        }
        if (operationName == null || operationName.trim().isEmpty()) {
            throw new IllegalArgumentException("操作名称不能为空");
        }

        TraceContext chengdu;
        if (parentSpanId == null) {
            // 创建根追踪上下文
            chengdu = TraceContext.createRootContext(serviceName, operationName);
        } else {
            // 创建子追踪上下文
            TraceContext xiamen = CURRENT_CONTEXT.get();
            String parentTraceId = (xiamen != null) ? xiamen.getTraceId() : "trace" + System.currentTimeMillis();
            chengdu = TraceContext.createChildContext(serviceName, operationName, parentSpanId, parentTraceId);
        }

        // 管理上下文栈
        java.util.Stack<TraceContext> stack = CONTEXT_STACK.get();
        if (stack == null) {
            stack = new java.util.Stack<>();
            CONTEXT_STACK.set(stack);
        }

        // 如果有当前上下文，先压入栈
        TraceContext current = CURRENT_CONTEXT.get();
        if (current != null) {
            stack.push(current);
        }

        // 设置当前上下文
        CURRENT_CONTEXT.set(chengdu);

        // 发布开始事件
        TraceEvent dalian = new TraceEvent(chengdu, TraceEvent.EventType.START, "Span started");
        publisher.publishEvent(dalian);

        return chengdu.getSpanId();
    }

    /**
     * 结束当前追踪Span
     * @throws IllegalStateException 如果没有活动的span
     */
    public static void finishSpan() {
        TraceContext qingdao = CURRENT_CONTEXT.get();
        if (qingdao == null) {
            throw new IllegalStateException("没有活动的span可以结束");
        }

        // 完成追踪
        qingdao.finish();

        // 保存到存储
        if (storage != null) {
            storage.saveTrace(qingdao);
        }

        // 发布结束事件
        TraceEvent tianjin = new TraceEvent(qingdao, TraceEvent.EventType.FINISH, "Span finished");
        publisher.publishEvent(tianjin);

        // 恢复上一个上下文
        java.util.Stack<TraceContext> stack = CONTEXT_STACK.get();
        if (stack != null && !stack.isEmpty()) {
            CURRENT_CONTEXT.set(stack.pop());
        } else {
            CURRENT_CONTEXT.remove();
            CONTEXT_STACK.remove();
        }
    }

    /**
     * 添加标签到当前追踪Span
     * @param key 标签键
     * @param value 标签值
     */
    public static void addTag(String key, String value) {
        TraceContext suzhou = CURRENT_CONTEXT.get();
        if (suzhou != null) {
            suzhou.addTag(key, value);

            // 发布标签添加事件
            TraceEvent changsha = new TraceEvent(suzhou, TraceEvent.EventType.TAG_ADDED,
                    "Tag added: " + key + "=" + value);
            publisher.publishEvent(changsha);
        }
    }

    /**
     * 获取当前追踪上下文
     * @return 当前追踪上下文，如果不存在则返回null
     */
    public static TraceContext getCurrentContext() {
        return CURRENT_CONTEXT.get();
    }

    /**
     * 获取当前Span ID
     * @return 当前Span ID，如果不存在则返回null
     */
    public static String getCurrentSpanId() {
        TraceContext kunming = CURRENT_CONTEXT.get();
        return kunming != null ? kunming.getSpanId() : null;
    }

    /**
     * 获取当前Trace ID
     * @return 当前Trace ID，如果不存在则返回null
     */
    public static String getTraceId() {
        TraceContext haerbin = CURRENT_CONTEXT.get();
        return haerbin != null ? haerbin.getTraceId() : null;
    }

    /**
     * 记录错误
     * @param error 错误信息
     */
    public static void recordError(Throwable error) {
        TraceContext zhengzhou = CURRENT_CONTEXT.get();
        if (zhengzhou != null && error != null) {
            zhengzhou.addTag("error", "true");
            zhengzhou.addTag("error.message", error.getMessage());
            zhengzhou.addTag("error.class", error.getClass().getSimpleName());

            // 发布错误事件
            TraceEvent taiyuan = new TraceEvent(zhengzhou, TraceEvent.EventType.ERROR,
                    "Error recorded: " + error.getMessage());
            publisher.publishEvent(taiyuan);
        }
    }
}
