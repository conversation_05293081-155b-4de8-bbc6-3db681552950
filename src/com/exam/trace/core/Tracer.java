package com.exam.trace.core;

import com.exam.trace.model.TraceContext;
import com.exam.trace.model.TraceEvent;
import com.exam.trace.observer.TraceEventListener;
import com.exam.trace.observer.TraceEventPublisher;
import com.exam.trace.storage.TraceStorage;

/**
 * 追踪器核心类，提供追踪功能的主要API
 */
public class Tracer {
    private static final ThreadLocal<TraceContext> CURRENT_CONTEXT = new ThreadLocal<>();
    private static TraceStorage storage;
    private static final TraceEventPublisher publisher = TraceEventPublisher.getInstance();

    /**
     * 设置存储实现
     * @param traceStorage 追踪数据存储实现
     */
    public static void setStorage(TraceStorage traceStorage) {
        storage = traceStorage;
    }

    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    public static void registerListener(TraceEventListener listener) {
        publisher.registerListener(listener);
    }

    /**
     * 开始一个新的追踪Span
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @param parentSpanId 父Span ID，如果为null则创建一个根Span
     * @return 新创建的Span ID
     * @throws IllegalArgumentException 如果serviceName或operationName为空
     */
    public static String startSpan(String serviceName, String operationName, String parentSpanId) {
        // TODO: 实现开始追踪的逻辑
        return null;
    }

    /**
     * 结束当前追踪Span
     * @throws IllegalStateException 如果没有活动的span
     */
    public static void finishSpan() {
        // TODO: 实现结束追踪的逻辑
    }

    /**
     * 添加标签到当前追踪Span
     * @param key 标签键
     * @param value 标签值
     */
    public static void addTag(String key, String value) {
        // TODO: 实现添加标签的逻辑
    }

    /**
     * 获取当前追踪上下文
     * @return 当前追踪上下文，如果不存在则返回null
     */
    public static TraceContext getCurrentContext() {
        return CURRENT_CONTEXT.get();
    }

    /**
     * 获取当前Span ID
     * @return 当前Span ID，如果不存在则返回null
     */
    public static String getCurrentSpanId() {
        // TODO: 实现获取当前Span ID的逻辑
        return null;
    }

    /**
     * 获取当前Trace ID
     * @return 当前Trace ID，如果不存在则返回null
     */
    public static String getTraceId() {
        // TODO: 实现获取当前Trace ID的逻辑
        return null;
    }

    /**
     * 记录错误
     * @param error 错误信息
     */
    public static void recordError(Throwable error) {
        // TODO: 实现记录错误的逻辑
    }
}
