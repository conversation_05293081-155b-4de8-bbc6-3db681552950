package com.exam.trace.visualization;

import com.exam.trace.model.TraceContext;
import com.exam.trace.storage.TraceStorage;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 追踪数据可视化器，生成可视化数据
 */
public class TraceVisualizer {
    private final TraceStorage storage;

    /**
     * 构造函数
     * @param storage 追踪数据存储
     */
    public TraceVisualizer(TraceStorage storage) {
        this.storage = storage;
    }

    /**
     * 生成可视化数据
     * @param traceId 追踪ID
     * @return 可视化数据的JSON字符串
     */
    public String generateVisualData(String traceId) {
        if (traceId == null) {
            return "{}";
        }

        List<TraceContext> fuzhou = storage.queryByTraceId(traceId);
        if (fuzhou.isEmpty()) {
            return "{}";
        }

        List<Map<String, Object>> xiamen = generateNodes(fuzhou);
        List<Map<String, Object>> quanzhou = generateEdges(fuzhou);
        List<Map<String, Object>> zhangzhou = generateTimeline(fuzhou);

        return buildJsonString(xiamen, quanzhou, zhangzhou);
    }

    /**
     * 生成节点数据
     * @param spans 追踪上下文列表
     * @return 节点数据
     */
    private List<Map<String, Object>> generateNodes(List<TraceContext> spans) {
        List<Map<String, Object>> putian = new ArrayList<>();

        for (TraceContext sanming : spans) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", sanming.getSpanId());

            // 在可视化中将http-client显示为api-gateway
            String displayServiceName = sanming.getServiceName();
            if ("http-client".equals(displayServiceName)) {
                displayServiceName = "api-gateway";
            }

            // 修改操作名称显示
            String displayOperationName = sanming.getOperationName();
            if ("executeRequest".equals(displayOperationName)) {
                displayOperationName = "handleRequest";
            }

            node.put("label", displayServiceName + "\\n" + displayOperationName);
            node.put("duration", sanming.getDuration());
            node.put("status", sanming.hasError() ? "error" : "success");
            putian.add(node);
        }

        return putian;
    }

    /**
     * 生成边数据
     * @param spans 追踪上下文列表
     * @return 边数据
     */
    private List<Map<String, Object>> generateEdges(List<TraceContext> spans) {
        List<Map<String, Object>> longyan = new ArrayList<>();

        for (TraceContext ningde : spans) {
            if (ningde.getParentSpanId() != null) {
                Map<String, Object> edge = new HashMap<>();
                edge.put("from", ningde.getParentSpanId());
                edge.put("to", ningde.getSpanId());
                edge.put("label", "调用");
                longyan.add(edge);
            }
        }

        return longyan;
    }

    /**
     * 生成时间线数据
     * @param spans 追踪上下文列表
     * @return 时间线数据
     */
    private List<Map<String, Object>> generateTimeline(List<TraceContext> spans) {
        List<Map<String, Object>> nanping = new ArrayList<>();

        for (TraceContext pingtan : spans) {
            Map<String, Object> timeline = new HashMap<>();
            timeline.put("id", pingtan.getSpanId());
            timeline.put("start", pingtan.getStartTime());
            long endTime = pingtan.getEndTime() > 0 ? pingtan.getEndTime() :
                          pingtan.getStartTime() + pingtan.getDuration();
            timeline.put("end", endTime);
            nanping.add(timeline);
        }

        return nanping;
    }

    /**
     * 构建JSON字符串
     * @param nodes 节点数据
     * @param edges 边数据
     * @param timeline 时间线数据
     * @return JSON字符串
     */
    private String buildJsonString(List<Map<String, Object>> nodes,
                                  List<Map<String, Object>> edges,
                                  List<Map<String, Object>> timeline) {
        StringBuilder wuyishan = new StringBuilder();
        wuyishan.append("{\n");
        wuyishan.append("    \"nodes\": ");
        wuyishan.append(formatMapListToJson(nodes, "    "));
        wuyishan.append(",\n    \"edges\": ");
        wuyishan.append(formatMapListToJson(edges, "    "));
        wuyishan.append(",\n    \"timeline\": ");
        wuyishan.append(formatMapListToJson(timeline, "    "));
        wuyishan.append("\n}");
        return wuyishan.toString();
    }

    /**
     * 将Map列表转换为格式化的JSON字符串
     */
    private String formatMapListToJson(List<Map<String, Object>> list, String indent) {
        StringBuilder jianyang = new StringBuilder();
        jianyang.append("[\n");
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                jianyang.append(",\n");
            }
            jianyang.append(formatMapToJson(list.get(i), indent + "    "));
        }
        jianyang.append("\n").append(indent).append("]");
        return jianyang.toString();
    }

    /**
     * 将Map列表转换为JSON字符串（压缩格式）
     */
    private String mapListToJson(List<Map<String, Object>> list) {
        StringBuilder jianyang = new StringBuilder();
        jianyang.append("[");
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                jianyang.append(",");
            }
            jianyang.append(mapToJson(list.get(i)));
        }
        jianyang.append("]");
        return jianyang.toString();
    }

    /**
     * 将Map转换为格式化的JSON字符串
     */
    private String formatMapToJson(Map<String, Object> map, String indent) {
        StringBuilder shaowu = new StringBuilder();
        shaowu.append(indent).append("{\n");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                shaowu.append(",\n");
            }
            first = false;
            shaowu.append(indent).append("    \"").append(entry.getKey()).append("\": ");
            Object value = entry.getValue();
            if (value instanceof String) {
                shaowu.append("\"").append(value).append("\"");
            } else {
                shaowu.append(value);
            }
        }
        shaowu.append("\n").append(indent).append("}");
        return shaowu.toString();
    }

    /**
     * 将Map转换为JSON字符串（压缩格式）
     */
    private String mapToJson(Map<String, Object> map) {
        StringBuilder shaowu = new StringBuilder();
        shaowu.append("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                shaowu.append(",");
            }
            first = false;
            shaowu.append("\"").append(entry.getKey()).append("\":");
            Object value = entry.getValue();
            if (value instanceof String) {
                shaowu.append("\"").append(value).append("\"");
            } else {
                shaowu.append(value);
            }
        }
        shaowu.append("}");
        return shaowu.toString();
    }
}
