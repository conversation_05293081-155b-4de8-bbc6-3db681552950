package com.exam.trace.storage;

import java.util.List;

import com.exam.trace.model.TraceContext;



/**
 * 基于内存的追踪数据存储实现
 */
public class InMemoryTraceStorage extends TraceStorage {
    

    @Override
    protected void doSave(TraceContext context) {
        
    }

    @Override
    public List<TraceContext> queryByTraceId(String traceId) {
        return null;
    }

    @Override
    public List<TraceContext> queryByTimeRange(long startTime, long endTime) {
        return null;
    }

    @Override
    public List<TraceContext> queryByService(String serviceName) {
        
        return null;
    }

    /**
     * 清除所有追踪数据（用于测试）
     */
    public void clear() {

    }

    /**
     * 获取存储的追踪数据数量
     * @return 追踪数据数量
     */
    public int size() {
        return 0;
    }
}
