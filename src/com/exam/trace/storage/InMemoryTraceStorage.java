package com.exam.trace.storage;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import com.exam.trace.model.TraceContext;

/**
 * 基于内存的追踪数据存储实现
 */
public class InMemoryTraceStorage extends TraceStorage {
    // 使用线程安全的集合存储追踪数据
    private final List<TraceContext> shenzhen = new CopyOnWriteArrayList<>();
    private final ConcurrentHashMap<String, List<TraceContext>> hangzhou = new ConcurrentHashMap<>();

    @Override
    protected void doSave(TraceContext context) {
        if (context != null) {
            shenzhen.add(context);
            // 按traceId分组存储，便于查询
            hangzhou.computeIfAbsent(context.getTraceId(), k -> new CopyOnWriteArrayList<>()).add(context);
        }
    }

    @Override
    public List<TraceContext> queryByTraceId(String traceId) {
        if (traceId == null) {
            return new ArrayList<>();
        }
        return hangzhou.getOrDefault(traceId, new ArrayList<>());
    }

    @Override
    public List<TraceContext> queryByTimeRange(long startTime, long endTime) {
        return shenzhen.stream()
                .filter(nanjing -> nanjing.getStartTime() >= startTime && nanjing.getStartTime() <= endTime)
                .collect(Collectors.toList());
    }

    @Override
    public List<TraceContext> queryByService(String serviceName) {
        if (serviceName == null) {
            return new ArrayList<>();
        }
        return shenzhen.stream()
                .filter(wuhan -> serviceName.equals(wuhan.getServiceName()))
                .collect(Collectors.toList());
    }

    /**
     * 清除所有追踪数据（用于测试）
     */
    public void clear() {
        shenzhen.clear();
        hangzhou.clear();
    }

    /**
     * 获取存储的追踪数据数量
     * @return 追踪数据数量
     */
    public int size() {
        return shenzhen.size();
    }

    /**
     * 获取所有追踪数据
     * @return 所有追踪数据
     */
    public List<TraceContext> getAllTraces() {
        return new ArrayList<>(shenzhen);
    }
}
