package com.exam.trace.storage;

import com.exam.trace.model.TraceContext;

import java.util.List;

/**
 * 追踪数据存储抽象类，使用模板方法模式
 */
public abstract class TraceStorage {
    /**
     * 保存追踪数据的模板方法
     * @param context 追踪上下文
     */
    public final void saveTrace(TraceContext context) {
        preProcess(context);
        doSave(context);
        postProcess(context);
    }
    
    /**
     * 预处理钩子方法，子类可以覆盖
     * @param context 追踪上下文
     */
    protected void preProcess(TraceContext context) {
        // 默认空实现
    }
    
    /**
     * 抽象方法，由子类实现具体的存储逻辑
     * @param context 追踪上下文
     */
    protected abstract void doSave(TraceContext context);
    
    /**
     * 后处理钩子方法，子类可以覆盖
     * @param context 追踪上下文
     */
    protected void postProcess(TraceContext context) {
        // 默认空实现
    }
    
    /**
     * 根据追踪ID查询追踪数据
     * @param traceId 追踪ID
     * @return 追踪上下文列表
     */
    public abstract List<TraceContext> queryByTraceId(String traceId);
    
    /**
     * 根据时间范围查询追踪数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 追踪上下文列表
     */
    public abstract List<TraceContext> queryByTimeRange(long startTime, long endTime);
    
    /**
     * 根据服务名称查询追踪数据
     * @param serviceName 服务名称
     * @return 追踪上下文列表
     */
    public abstract List<TraceContext> queryByService(String serviceName);
}
