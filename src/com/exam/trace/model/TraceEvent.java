package com.exam.trace.model;

import java.io.Serializable;

/**
 * 追踪事件类，用于在追踪过程中发布事件
 */
public class TraceEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 事件类型枚举
    public enum EventType {
        START,      // 开始追踪
        FINISH,     // 结束追踪
        ERROR,      // 错误事件
        TAG_ADDED   // 添加标签
    }

    private TraceContext context;   // 关联的追踪上下文
    private EventType eventType;    // 事件类型
    private long timestamp;         // 事件发生时间戳
    private String payload;         // 事件负载信息

    /**
     * 构造函数
     * @param context 追踪上下文
     * @param eventType 事件类型
     * @param payload 事件负载
     */
    public TraceEvent(TraceContext context, EventType eventType, String payload) {
        this.context = context;
        this.eventType = eventType;
        this.payload = payload;
        this.timestamp = System.currentTimeMillis();
    }

    // TODO: 实现必要的getter和setter方法
    public TraceContext getContext() {
        return context;
    }

    public EventType getEventType() {
        return eventType;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getPayload() {
        return payload;
    }
}
