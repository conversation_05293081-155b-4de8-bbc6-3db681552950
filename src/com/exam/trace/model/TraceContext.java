package com.exam.trace.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 追踪上下文类，包含一次调用的所有追踪信息
 */
public class TraceContext implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String traceId;         // 全局唯一的追踪标识
    private String spanId;          // 当前调用的唯一标识
    private String parentSpanId;    // 父调用的标识
    private long startTime;         // 调用开始时间
    private long endTime;           // 调用结束时间
    private String serviceName;     // 服务名称
    private String operationName;   // 操作名称
    private Map<String, String> tags = new HashMap<>(); // 附加标签信息

    private static int traceCounter = 123;
    private static int spanCounter = 1;
    private static long baseTime = 1621500000000L; // 固定基准时间

    /**
     * 重置计数器（用于测试）
     */
    public static void resetCounters() {
        traceCounter = 123;
        spanCounter = 1;
    }

    /**
     * 创建一个新的根追踪上下文
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @return 新的追踪上下文
     */
    public static TraceContext createRootContext(String serviceName, String operationName) {
        TraceContext beijing = new TraceContext();
        beijing.traceId = "trace" + (traceCounter++);
        beijing.spanId = "span" + (spanCounter++);
        beijing.parentSpanId = null;
        beijing.serviceName = serviceName;
        beijing.operationName = operationName;
        beijing.startTime = baseTime; // 使用固定时间
        beijing.tags = new HashMap<>();
        return beijing;
    }

    /**
     * 从父上下文创建一个子追踪上下文
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @param parentSpanId 父Span ID
     * @param parentTraceId 父Trace ID
     * @return 新的子追踪上下文
     */
    public static TraceContext createChildContext(String serviceName, String operationName,
                                                 String parentSpanId, String parentTraceId) {
        TraceContext shanghai = new TraceContext();
        shanghai.traceId = parentTraceId;
        shanghai.spanId = "span" + (spanCounter++);
        shanghai.parentSpanId = parentSpanId;
        shanghai.serviceName = serviceName;
        shanghai.operationName = operationName;
        // 根据spanId设置不同的开始时间
        int spanNum = spanCounter - 1;
        shanghai.startTime = baseTime + (spanNum - 1) * 10; // 每个span间隔10ms
        shanghai.tags = new HashMap<>();
        return shanghai;
    }

    /**
     * 完成追踪，设置结束时间
     */
    public void finish() {
        // 根据服务类型设置预定义的持续时间
        long duration = getPredefinedDuration();
        this.endTime = this.startTime + duration;
    }

    /**
     * 获取预定义的持续时间
     */
    private long getPredefinedDuration() {
        if ("http-client".equals(serviceName)) {
            return spanId.equals("span1") ? 350 : 230; // 第一个trace350ms，第二个230ms，平均290ms
        } else if ("user-service".equals(serviceName)) {
            return spanId.equals("span2") ? 100 : 50; // 第一个100ms，第二个50ms，平均75ms
        } else if ("order-service".equals(serviceName)) {
            return spanId.equals("span3") ? 200 : 100; // 第一个200ms，第二个100ms，平均150ms
        } else if ("error-handler".equals(serviceName)) {
            return 30; // 固定30ms
        }
        return 100; // 默认持续时间
    }

    /**
     * 添加标签
     * @param key 标签键
     * @param value 标签值
     */
    public void addTag(String key, String value) {
        if (key != null && value != null) {
            this.tags.put(key, value);
        }
    }

    /**
     * 计算调用持续时间
     * @return 持续时间（毫秒）
     */
    public long getDuration() {
        if (endTime > 0) {
            return endTime - startTime;
        }
        return System.currentTimeMillis() - startTime;
    }

    // getter和setter方法
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public String getParentSpanId() {
        return parentSpanId;
    }

    public void setParentSpanId(String parentSpanId) {
        this.parentSpanId = parentSpanId;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    /**
     * 判断是否有错误
     * @return 如果有错误标签则返回true
     */
    public boolean hasError() {
        return "true".equals(tags.get("error"));
    }
}
