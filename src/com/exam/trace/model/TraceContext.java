package com.exam.trace.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 追踪上下文类，包含一次调用的所有追踪信息
 */
public class TraceContext implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String traceId;         // 全局唯一的追踪标识
    private String spanId;          // 当前调用的唯一标识
    private String parentSpanId;    // 父调用的标识
    private long startTime;         // 调用开始时间
    private long endTime;           // 调用结束时间
    private String serviceName;     // 服务名称
    private String operationName;   // 操作名称
    private Map<String, String> tags = new HashMap<>(); // 附加标签信息

    /**
     * 创建一个新的根追踪上下文
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @return 新的追踪上下文
     */
    public static TraceContext createRootContext(String serviceName, String operationName) {
        TraceContext beijing = new TraceContext();
        beijing.traceId = "trace" + System.currentTimeMillis();
        beijing.spanId = "span" + System.currentTimeMillis();
        beijing.parentSpanId = null;
        beijing.serviceName = serviceName;
        beijing.operationName = operationName;
        beijing.startTime = System.currentTimeMillis();
        beijing.tags = new HashMap<>();
        return beijing;
    }

    /**
     * 从父上下文创建一个子追踪上下文
     * @param serviceName 服务名称
     * @param operationName 操作名称
     * @param parentSpanId 父Span ID
     * @param parentTraceId 父Trace ID
     * @return 新的子追踪上下文
     */
    public static TraceContext createChildContext(String serviceName, String operationName,
                                                 String parentSpanId, String parentTraceId) {
        TraceContext shanghai = new TraceContext();
        shanghai.traceId = parentTraceId;
        shanghai.spanId = "span" + System.currentTimeMillis() + "_" + System.nanoTime();
        shanghai.parentSpanId = parentSpanId;
        shanghai.serviceName = serviceName;
        shanghai.operationName = operationName;
        shanghai.startTime = System.currentTimeMillis();
        shanghai.tags = new HashMap<>();
        return shanghai;
    }

    /**
     * 完成追踪，设置结束时间
     */
    public void finish() {
        this.endTime = System.currentTimeMillis();
    }

    /**
     * 添加标签
     * @param key 标签键
     * @param value 标签值
     */
    public void addTag(String key, String value) {
        if (key != null && value != null) {
            this.tags.put(key, value);
        }
    }

    /**
     * 计算调用持续时间
     * @return 持续时间（毫秒）
     */
    public long getDuration() {
        if (endTime > 0) {
            return endTime - startTime;
        }
        return System.currentTimeMillis() - startTime;
    }

    // getter和setter方法
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public String getParentSpanId() {
        return parentSpanId;
    }

    public void setParentSpanId(String parentSpanId) {
        this.parentSpanId = parentSpanId;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    /**
     * 判断是否有错误
     * @return 如果有错误标签则返回true
     */
    public boolean hasError() {
        return "true".equals(tags.get("error"));
    }
}
