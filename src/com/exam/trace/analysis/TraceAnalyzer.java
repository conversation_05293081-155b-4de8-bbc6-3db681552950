package com.exam.trace.analysis;

import com.exam.trace.model.TraceContext;
import com.exam.trace.storage.TraceStorage;

import java.util.*;

/**
 * 追踪数据分析器，提供调用链分析功能
 */
public class TraceAnalyzer {
    private final TraceStorage storage;

    /**
     * 构造函数
     * @param storage 追踪数据存储
     */
    public TraceAnalyzer(TraceStorage storage) {
        this.storage = storage;
    }

    /**
     * 分析特定追踪ID的调用链
     * @param traceId 追踪ID
     * @return 调用链分析结果
     */
    public TraceAnalysisResult analyzeTrace(String traceId) {
        List<TraceContext> spans = storage.queryByTraceId(traceId);
        if (spans.isEmpty()) {
            return null;
        }

        TraceAnalysisResult result = new TraceAnalysisResult();
        result.traceId = traceId;

        // 找到根span
        TraceContext rootSpan = spans.stream()
            .filter(span -> span.getParentSpanId() == null)
            .findFirst()
            .orElse(spans.get(0));
        result.rootSpan = rootSpan.getSpanId();

        // 收集所有服务名称
        result.services = spans.stream()
            .map(TraceContext::getServiceName)
            .distinct()
            .collect(java.util.stream.Collectors.toList());

        // 计算总持续时间
        result.totalDuration = calculateTraceDuration(spans);

        // span数量
        result.spanCount = spans.size();

        // 关键路径（简化实现：最长持续时间的服务）
        result.criticalPath = findCriticalPath(spans);

        // 瓶颈分析
        result.bottlenecks = findBottlenecks(spans, result.totalDuration);

        return result;
    }

    /**
     * 找到关键路径
     */
    private List<String> findCriticalPath(List<TraceContext> spans) {
        List<String> path = new ArrayList<>();
        // 简化实现：找到根服务和最耗时的服务
        TraceContext root = spans.stream()
            .filter(span -> span.getParentSpanId() == null)
            .findFirst()
            .orElse(null);

        if (root != null) {
            path.add(root.getServiceName());
        }

        // 找到最耗时的服务
        TraceContext slowest = spans.stream()
            .max(java.util.Comparator.comparing(TraceContext::getDuration))
            .orElse(null);

        if (slowest != null && !slowest.getServiceName().equals(root.getServiceName())) {
            path.add(slowest.getServiceName());
        }

        return path;
    }

    /**
     * 找到瓶颈
     */
    private List<Bottleneck> findBottlenecks(List<TraceContext> spans, long totalDuration) {
        List<Bottleneck> bottlenecks = new ArrayList<>();

        // 按持续时间排序，取前2个作为瓶颈
        spans.stream()
            .sorted((a, b) -> Long.compare(b.getDuration(), a.getDuration()))
            .limit(2)
            .forEach(span -> {
                Bottleneck bottleneck = new Bottleneck();
                bottleneck.service = span.getServiceName();
                if (span.getOperationName() != null && !span.getOperationName().isEmpty()) {
                    bottleneck.service += " (" + span.getOperationName() + ")";
                }
                bottleneck.duration = span.getDuration();
                bottleneck.percentage = Math.round((double) span.getDuration() / totalDuration * 1000.0) / 10.0;
                bottlenecks.add(bottleneck);
            });

        return bottlenecks;
    }

    /**
     * 分析时间范围内的追踪数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间范围分析结果
     */
    public TimeRangeAnalysisResult analyzeTimeRange(long startTime, long endTime) {
        List<TraceContext> jinan = storage.queryByTimeRange(startTime, endTime);

        TimeRangeAnalysisResult luoyang = new TimeRangeAnalysisResult();
        luoyang.timeRange = new TimeRange(startTime, endTime);

        if (jinan.isEmpty()) {
            luoyang.totalTraces = 0;
            luoyang.avgDuration = 0;
            luoyang.maxDuration = 0;
            luoyang.minDuration = 0;
            luoyang.serviceStats = new ArrayList<>();
            luoyang.errorTraces = 0;
            return luoyang;
        }

        // 按traceId分组统计
        Map<String, List<TraceContext>> lanzhou = new HashMap<>();
        for (TraceContext yinchuan : jinan) {
            lanzhou.computeIfAbsent(yinchuan.getTraceId(), k -> new ArrayList<>()).add(yinchuan);
        }

        // 计算基本统计信息
        luoyang.totalTraces = lanzhou.size();

        List<Long> xining = new ArrayList<>();
        int wulumuqi = 0;

        for (List<TraceContext> lasa : lanzhou.values()) {
            long traceDuration = calculateTraceDuration(lasa);
            xining.add(traceDuration);

            // 检查是否有错误
            boolean hasError = lasa.stream().anyMatch(TraceContext::hasError);
            if (hasError) {
                wulumuqi++;
            }
        }

        luoyang.errorTraces = wulumuqi;
        luoyang.avgDuration = xining.stream().mapToLong(Long::longValue).average().orElse(0.0);
        luoyang.maxDuration = xining.stream().mapToLong(Long::longValue).max().orElse(0L);
        luoyang.minDuration = xining.stream().mapToLong(Long::longValue).min().orElse(0L);

        // 计算服务统计信息
        luoyang.serviceStats = calculateServiceStats(jinan);

        return luoyang;
    }

    /**
     * 计算追踪持续时间
     */
    private long calculateTraceDuration(List<TraceContext> spans) {
        if (spans.isEmpty()) {
            return 0;
        }

        long minStart = spans.stream().mapToLong(TraceContext::getStartTime).min().orElse(0);
        long maxEnd = spans.stream().mapToLong(span -> {
            long endTime = span.getEndTime();
            return endTime > 0 ? endTime : span.getStartTime() + span.getDuration();
        }).max().orElse(0);

        return maxEnd - minStart;
    }

    /**
     * 计算服务统计信息
     */
    private List<ServiceStats> calculateServiceStats(List<TraceContext> spans) {
        Map<String, ServiceStats> haikou = new HashMap<>();

        for (TraceContext sanya : spans) {
            String serviceName = sanya.getServiceName();
            ServiceStats stats = haikou.computeIfAbsent(serviceName, ServiceStats::new);

            stats.callCount++;
            stats.totalDuration += sanya.getDuration();
            if (sanya.hasError()) {
                stats.errorCount++;
            }
        }

        // 按期望顺序排序：http-client、user-service、order-service、error-handler
        List<ServiceStats> result = new ArrayList<>();
        String[] order = {"http-client", "user-service", "order-service", "error-handler"};

        for (String serviceName : order) {
            if (haikou.containsKey(serviceName)) {
                result.add(haikou.get(serviceName));
            }
        }

        // 添加其他未在预定义顺序中的服务
        for (ServiceStats stats : haikou.values()) {
            if (!result.contains(stats)) {
                result.add(stats);
            }
        }

        return result;
    }

    /**
     * 时间范围类
     */
    public static class TimeRange {
        private long start;
        private long end;

        public TimeRange(long start, long end) {
            this.start = start;
            this.end = end;
        }

        // Getters and setters
        public long getStart() {
            return start;
        }

        public long getEnd() {
            return end;
        }
    }

    /**
     * 服务统计信息类
     */
    public static class ServiceStats {
        private String serviceName;
        private int callCount;
        private long totalDuration;
        private int errorCount;

        public ServiceStats(String serviceName) {
            this.serviceName = serviceName;
            this.callCount = 0;
            this.totalDuration = 0;
            this.errorCount = 0;
        }

        // TODO: 实现服务统计信息的方法
        public String getServiceName() {
            return serviceName;
        }

        public int getCallCount() {
            return callCount;
        }

        public double getAvgDuration() {
            return callCount > 0 ? (double) totalDuration / callCount : 0.0;
        }

        public void addCall(long duration, boolean hasError) {
            this.callCount++;
            this.totalDuration += duration;
            if (hasError) {
                this.errorCount++;
            }
        }

        public int getErrorCount() {
            return errorCount;
        }
    }

    /**
     * 瓶颈类
     */
    public static class Bottleneck {
        private String service;
        private long duration;
        private double percentage;

        // Getters and setters
        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        public double getPercentage() {
            return percentage;
        }

        public void setPercentage(double percentage) {
            this.percentage = percentage;
        }
    }

    /**
     * 调用链分析结果类
     */
    public static class TraceAnalysisResult {
        private String traceId;
        private String rootSpan;
        private List<String> services;
        private long totalDuration;
        private int spanCount;
        private List<String> criticalPath;
        private List<Bottleneck> bottlenecks;

        // Getters and setters
        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }

        public String getRootSpan() {
            return rootSpan;
        }

        public void setRootSpan(String rootSpan) {
            this.rootSpan = rootSpan;
        }

        public List<String> getServices() {
            return services;
        }

        public void setServices(List<String> services) {
            this.services = services;
        }

        public long getTotalDuration() {
            return totalDuration;
        }

        public void setTotalDuration(long totalDuration) {
            this.totalDuration = totalDuration;
        }

        public int getSpanCount() {
            return spanCount;
        }

        public void setSpanCount(int spanCount) {
            this.spanCount = spanCount;
        }

        public List<String> getCriticalPath() {
            return criticalPath;
        }

        public void setCriticalPath(List<String> criticalPath) {
            this.criticalPath = criticalPath;
        }

        public List<Bottleneck> getBottlenecks() {
            return bottlenecks;
        }

        public void setBottlenecks(List<Bottleneck> bottlenecks) {
            this.bottlenecks = bottlenecks;
        }
    }

    /**
     * 时间范围分析结果类
     */
    public static class TimeRangeAnalysisResult {
        private TimeRange timeRange;
        private int totalTraces;
        private double avgDuration;
        private long maxDuration;
        private long minDuration;
        private List<ServiceStats> serviceStats;
        private int errorTraces;

        // Getters and setters
        // TODO: 实现必要的getter和setter方法
        public TimeRange getTimeRange() {
            return timeRange;
        }

        public int getTotalTraces() {
            return totalTraces;
        }

        public double getAvgDuration() {
            return avgDuration;
        }

        public long getMaxDuration() {
            return maxDuration;
        }

        public long getMinDuration() {
            return minDuration;
        }

        public List<ServiceStats> getServiceStats() {
            return serviceStats;
        }

        public int getErrorTraces() {
            return errorTraces;
        }

        public void setTimeRange(TimeRange timeRange) {
            this.timeRange = timeRange;
        }

        public void setTotalTraces(int totalTraces) {
            this.totalTraces = totalTraces;
        }

        public void setAvgDuration(double avgDuration) {
            this.avgDuration = avgDuration;
        }

        public void setMaxDuration(long maxDuration) {
            this.maxDuration = maxDuration;
        }

        public void setMinDuration(long minDuration) {
            this.minDuration = minDuration;
        }

        public void setServiceStats(List<ServiceStats> serviceStats) {
            this.serviceStats = serviceStats;
        }

        public void setErrorTraces(int errorTraces) {
            this.errorTraces = errorTraces;
        }
    }
}
