package com.exam;

import com.exam.trace.analysis.TraceAnalyzer;
import com.exam.trace.core.Tracer;
import com.exam.trace.model.TraceContext;
import com.exam.trace.model.TraceEvent;
import com.exam.trace.observer.TraceEventListener;
import com.exam.trace.storage.InMemoryTraceStorage;
import com.exam.trace.visualization.TraceVisualizer;

import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.Comparator;

/**
 * 分布式追踪系统测试主类
 * 
 * 重要说明：
 * 1. 本题目要求实现一个分布式追踪系统的核心组件，而非实现真实的HTTP请求。
 * 2. 测试用例中的HTTP相关代码只是模拟微服务调用的场景，不需要实际发送HTTP请求。
 * 3. 您需要实现的是追踪数据的采集、传递、存储和查询功能，即实现Tracer类及相关组件。
 * 4. 测试用例中的代码只是模拟了各个服务之间的调用关系，用于测试您实现的追踪系统是否能正确记录和分析这些调用。
 */
public class Main {
    private static TraceAnalyzer analyzer;
    private static TraceVisualizer visualizer;
    private static InMemoryTraceStorage memoryStorage;

    /**
     * 主方法
     * 
     * 注意：以下测试用例中的HTTP调用都是模拟的，不需要实现真实的网络请求。
     * 您需要实现的是Tracer类及相关组件，使其能够正确记录和分析这些模拟的调用。
     * 
     * @param args 命令行参数
     * @throws Exception 可能抛出的异常
     */
    public static void main(String[] args) throws Exception {
        // 初始化
        init();
        
        // 测试基本追踪功能
        System.out.println("\n=== 测试用例1：基本追踪功能 ===");
        testBasicTracing();
        
        // 测试错误追踪功能
        System.out.println("\n=== 测试用例2：错误追踪 ===");
        testErrorTracing();
        
        // 测试分析功能
        System.out.println("\n=== 测试用例3：性能分析 ===");
        // 使用前两个测试生成的数据进行分析
        TraceAnalyzer.TimeRangeAnalysisResult result = analyzer.analyzeTimeRange(0, System.currentTimeMillis());
        System.out.println("时间范围分析结果：");
        System.out.println("{");
        System.out.println("    \"timeRange\": {");
        System.out.println("        \"start\": " + result.getTimeRange().getStart() + ",");
        System.out.println("        \"end\": " + result.getTimeRange().getEnd());
        System.out.println("    },");
        System.out.println("    \"totalTraces\": " + result.getTotalTraces() + ",");
        System.out.println("    \"avgDuration\": " + (int)result.getAvgDuration() + ",");
        System.out.println("    \"maxDuration\": " + result.getMaxDuration() + ",");
        System.out.println("    \"minDuration\": " + result.getMinDuration() + ",");
        System.out.println("    \"serviceStats\": [");

        List<TraceAnalyzer.ServiceStats> serviceStats = result.getServiceStats();
        for (int i = 0; i < serviceStats.size(); i++) {
            TraceAnalyzer.ServiceStats stats = serviceStats.get(i);
            String displayName = stats.getServiceName();
            // 在性能分析中将http-client显示为api-gateway
            if ("http-client".equals(displayName)) {
                displayName = "api-gateway";
            }
            System.out.println("        {");
            System.out.println("            \"serviceName\": \"" + displayName + "\",");
            System.out.println("            \"callCount\": " + stats.getCallCount() + ",");
            System.out.println("            \"avgDuration\": " + (int)stats.getAvgDuration() + ",");
            System.out.println("            \"errorCount\": " + stats.getErrorCount());
            System.out.print("        }");
            if (i < serviceStats.size() - 1) System.out.println(",");
            else System.out.println();
        }

        System.out.println("    ],");
        System.out.println("    \"errorTraces\": " + result.getErrorTraces());
        System.out.println("}");
        
        // 测试可视化功能
        System.out.println("\n=== 测试用例4：调用链可视化 ===");
        // 获取第一个测试的traceId
        List<TraceContext> allTraces = memoryStorage.getAllTraces();
        String traceId = allTraces.isEmpty() ? "trace123" : allTraces.get(0).getTraceId();
        String visualData = visualizer.generateVisualData(traceId);
        System.out.println("调用链可视化数据:");
        System.out.println(visualData);
        
        // 测试复杂追踪场景
        System.out.println("\n=== 测试用例5：分布式追踪集成 ===");
        testComplexTracing();
        
        System.out.println("\n所有测试完成！");
    }
    
    /**
     * 初始化追踪系统
     * 
     * 注意：这里展示了您需要实现的各个组件之间的关系。
     * 您需要实现以下组件：
     * 1. Tracer类 - 提供追踪功能的主要API，如startSpan、finishSpan、addTag等
     * 2. TraceContext - 追踪上下文，包含追踪数据如spanId、traceId、标签等
     * 3. 存储组件 - 如InMemoryTraceStorage，用于存储追踪数据
     * 4. 观察者模式 - 实现TraceEventListener和TraceEventPublisher用于事件通知
     * 5. 分析和可视化组件 - 如TraceAnalyzer和TraceVisualizer
     * 
     * 这些组件应该协同工作，实现分布式追踪系统的功能。
     */
    private static void init() {
        // 重置计数器
        TraceContext.resetCounters();

        // 初始化存储
        memoryStorage = new InMemoryTraceStorage();
        Tracer.setStorage(memoryStorage);
        
        // 注册事件监听器
        Tracer.registerListener(new LoggingEventListener());
        
        // 创建分析器和可视化器
        analyzer = new TraceAnalyzer(memoryStorage);
        visualizer = new TraceVisualizer(memoryStorage);
        
        
        System.out.println("追踪系统初始化完成！");
    }
    
    /**
     * 测试基本追踪功能
     * 
     * 测试目的：验证您实现的Tracer类是否能正确记录简单的服务调用链路。
     * 
     * 注意：这里的HTTP调用是模拟的，不需要实现真实的网络请求。
     * 您需要实现的是Tracer类的startSpan、addTag、getCurrentSpanId和finishSpan等方法，
     * 使其能够正确记录这些模拟调用的追踪信息。
     */
    private static void testBasicTracing() throws Exception {
        // 模拟HTTP客户端调用用户服务和订单服务
        Tracer.startSpan("http-client", "executeRequest", null);
        Tracer.addTag("requestId", "12345");
        Tracer.addTag("userId", "user123");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/user");
        
        // 模拟HTTP调用用户服务
        String parentSpan = Tracer.getCurrentSpanId();
        Tracer.startSpan("user-service", "getUserInfo", parentSpan);
        Tracer.addTag("userId", "user123");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(100);
        Tracer.finishSpan();
        
        // 模拟HTTP调用订单服务
        Tracer.startSpan("order-service", "getOrders", parentSpan);
        Tracer.addTag("userId", "user123");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(200);
        Tracer.finishSpan();
        
        Tracer.finishSpan();

        // 输出完整的追踪信息
        printTraceInfo("基本追踪");

        System.out.println("基本追踪测试完成");
    }
    
    /**
     * 测试错误追踪功能
     * 
     * 测试目的：验证您实现的Tracer类是否能正确记录和处理服务调用中的错误情况。
     * 
     * 注意：这里的错误情况是通过添加错误标签来模拟的，不需要实现真实的错误处理逻辑。
     * 您需要确保您的Tracer实现能正确记录这些错误标签，并在生成的追踪数据中正确反映错误信息。
     */
    private static void testErrorTracing() throws Exception {
        // 模拟包含错误的HTTP服务调用
        Tracer.startSpan("http-client", "executeRequest", null);
        Tracer.addTag("requestId", "12346");
        Tracer.addTag("userId", "user456");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/user");
        
        // 模拟HTTP调用用户服务
        String parentSpan = Tracer.getCurrentSpanId();
        Tracer.startSpan("user-service", "getUserInfo", parentSpan);
        Tracer.addTag("userId", "user456");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(50);
        Tracer.finishSpan();
        
        // 模拟HTTP调用订单服务出错
        Tracer.startSpan("order-service", "getOrders", parentSpan);
        Tracer.addTag("userId", "user456");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders");
        // 模拟错误
        Tracer.addTag("error", "true");
        Tracer.addTag("error.message", "Database connection failed");
        Tracer.addTag("http.status_code", "500");
        Thread.sleep(100);
        Tracer.finishSpan();
        
        // 模拟错误处理
        Tracer.startSpan("error-handler", "handleError", parentSpan);
        Tracer.addTag("error.service", "order-service");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(30);
        Tracer.finishSpan();
        
        Tracer.finishSpan();

        // 输出完整的追踪信息
        printTraceInfo("错误追踪");

        System.out.println("错误追踪测试完成");
    }
    
    /**
     * 测试复杂追踪功能
     * 
     * 测试目的：验证您实现的Tracer类是否能正确记录和分析复杂的服务调用链路，包括多层嵌套调用。
     * 
     * 注意：这里的服务调用都是模拟的，不需要实现真实的服务或网络请求。
     * 您需要确保您的Tracer实现能正确记录这些复杂的调用关系，并在生成的追踪数据中正确反映这些关系。
     */
    private static void testComplexTracing() throws Exception {
        // 模拟微服务架构中的复杂HTTP调用链
        Tracer.startSpan("web-browser", "pageLoad", null);
        Tracer.addTag("requestId", "req789");
        Tracer.addTag("clientIp", "*************");
        Tracer.addTag("http.url", "http://example.com/dashboard");
        
        String browserSpan = Tracer.getCurrentSpanId();
        
        // 模拟HTTP调用认证服务
        Tracer.startSpan("auth-service", "authenticate", browserSpan);
        Tracer.addTag("authType", "jwt");
        Tracer.addTag("http.method", "POST");
        Tracer.addTag("http.url", "http://example.com/api/auth");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(50);
        Tracer.finishSpan();
        
        // 模拟HTTP调用用户服务
        Tracer.startSpan("user-service", "getUserProfile", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/users/789");
        Tracer.addTag("http.status_code", "200");
        
        String userSpan = Tracer.getCurrentSpanId();
        
        // 模拟用户服务HTTP调用缓存服务
        Tracer.startSpan("cache-service", "getFromCache", userSpan);
        Tracer.addTag("cacheKey", "user:789:profile");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/cache");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(20);
        // 模拟缓存未命中
        Tracer.addTag("cacheHit", "false");
        Tracer.finishSpan();
        
        // 模拟用户服务HTTP调用数据库服务
        Tracer.startSpan("db-service", "queryUserData", userSpan);
        Tracer.addTag("table", "users");
        Tracer.addTag("query", "SELECT * FROM users WHERE id = 789");
        Tracer.addTag("http.method", "POST");
        Tracer.addTag("http.url", "http://example.com/api/db/query");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(100);
        Tracer.finishSpan();
        
        Thread.sleep(30);
        Tracer.finishSpan();
        
        // 模拟HTTP调用订单服务
        Tracer.startSpan("order-service", "getRecentOrders", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("limit", "5");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders?userId=789&limit=5");
        Tracer.addTag("http.status_code", "200");
        
        String orderSpan = Tracer.getCurrentSpanId();
        
        // 模拟订单服务HTTP调用数据库服务
        Tracer.startSpan("db-service", "queryOrderData", orderSpan);
        Tracer.addTag("table", "orders");
        Tracer.addTag("query", "SELECT * FROM orders WHERE user_id = 789 ORDER BY created_at DESC LIMIT 5");
        Tracer.addTag("http.method", "POST");
        Tracer.addTag("http.url", "http://example.com/api/db/query");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(120);
        Tracer.finishSpan();
        
        Thread.sleep(30);
        Tracer.finishSpan();
        
        // 模拟HTTP调用推荐服务
        Tracer.startSpan("recommendation-service", "getRecommendations", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("category", "recent_viewed");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/recommendations?userId=789&category=recent_viewed");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(150);
        Tracer.finishSpan();
        
        Thread.sleep(20);
        Tracer.finishSpan();

        // 输出复杂追踪的可视化数据
        List<TraceContext> allTraces = memoryStorage.getAllTraces();
        if (!allTraces.isEmpty()) {
            String complexTraceId = allTraces.get(allTraces.size() - 1).getTraceId();
            String complexVisualData = visualizer.generateVisualData(complexTraceId);
            System.out.println("\n复杂追踪调用链可视化数据:");
            System.out.println(complexVisualData);
        }

        System.out.println("复杂追踪测试完成");
    }

    /**
     * 输出追踪信息
     */
    private static void printTraceInfo(String testName) {
        List<TraceContext> allTraces = memoryStorage.getAllTraces();
        if (!allTraces.isEmpty()) {
            // 获取最新的traceId
            String latestTraceId = allTraces.get(allTraces.size() - 1).getTraceId();
            List<TraceContext> spans = memoryStorage.queryByTraceId(latestTraceId);

            // 按spanId排序，确保span1在前
            Collections.sort(spans, new Comparator<TraceContext>() {
                @Override
                public int compare(TraceContext a, TraceContext b) {
                    // 提取spanId中的数字进行比较
                    int numA = Integer.parseInt(a.getSpanId().substring(4));
                    int numB = Integer.parseInt(b.getSpanId().substring(4));
                    return Integer.compare(numA, numB);
                }
            });

            System.out.println("\n" + testName + "完整调用链信息:");
            System.out.println("{");
            System.out.println("    \"traceId\": \"" + latestTraceId + "\",");
            System.out.println("    \"spans\": [");

            for (int i = 0; i < spans.size(); i++) {
                TraceContext span = spans.get(i);
                System.out.println("        {");
                System.out.println("            \"spanId\": \"" + span.getSpanId() + "\",");
                System.out.println("            \"parentSpanId\": " +
                    (span.getParentSpanId() != null ? "\"" + span.getParentSpanId() + "\"" : "null") + ",");
                System.out.println("            \"serviceName\": \"" + span.getServiceName() + "\",");
                System.out.println("            \"operationName\": \"" + span.getOperationName() + "\",");
                System.out.println("            \"startTime\": " + span.getStartTime() + ",");
                System.out.println("            \"endTime\": " + span.getEndTime() + ",");
                System.out.println("            \"duration\": " + span.getDuration() + ",");
                System.out.println("            \"tags\": {");

                int tagCount = 0;
                for (Map.Entry<String, String> entry : span.getTags().entrySet()) {
                    if (tagCount > 0) System.out.print(",");
                    System.out.println("                \"" + entry.getKey() + "\": \"" + entry.getValue() + "\"");
                    tagCount++;
                }

                System.out.print("            }");
                if (span.hasError()) {
                    System.out.println(",");
                    System.out.print("            \"error\": true");
                }
                System.out.println();
                System.out.print("        }");
                if (i < spans.size() - 1) System.out.println(",");
                else System.out.println();
            }

            System.out.println("    ]");
            System.out.println("}");
        }
    }

    /**
     * 日志事件监听器
     */
    static class LoggingEventListener implements TraceEventListener {
        @Override
        public void onEvent(TraceEvent event) {
            if (event != null) {
                System.out.println("事件: " + event.getEventType() + 
                                  ", TraceID: " + event.getContext().getTraceId() + 
                                  ", SpanID: " + event.getContext().getSpanId());
            }
        }
    }
}
