#### 测试用例1：基本追踪功能
```
// 输入：模拟HTTP客户端调用用户服务和订单服务
Tracer.startSpan("http-client", "executeRequest", null);
    Tracer.addTag("requestId", "12345");
    Tracer.addTag("userId", "user123");
    Tracer.addTag("http.method", "GET");
    Tracer.addTag("http.url", "http://example.com/api/user");
    
    // 模拟HTTP调用用户服务
    String parentSpan = Tracer.getCurrentSpanId();
    Tracer.startSpan("user-service", "getUserInfo", parentSpan);
        Tracer.addTag("userId", "user123");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(100);
    Tracer.finishSpan();
    
    // 模拟HTTP调用订单服务
    Tracer.startSpan("order-service", "getOrders", parentSpan);
        Tracer.addTag("userId", "user123");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(200);
    Tracer.finishSpan();
    
Tracer.finishSpan();

// 期望输出：完整的调用链信息
{
    "traceId": "trace123",
    "spans": [
        {
            "spanId": "span1",
            "parentSpanId": null,
            "serviceName": "http-client",
            "operationName": "executeRequest",
            "startTime": 1621500000000,
            "endTime": 1621500000350,
            "duration": 350,
            "tags": {
                "requestId": "12345",
                "userId": "user123",
                "http.method": "GET",
                "http.url": "http://example.com/api/user"
            }
        },
        {
            "spanId": "span2",
            "parentSpanId": "span1",
            "serviceName": "user-service",
            "operationName": "getUserInfo",
            "startTime": 1621500000010,
            "endTime": 1621500000110,
            "duration": 100,
            "tags": {
                "userId": "user123",
                "http.status_code": "200"
            }
        },
        {
            "spanId": "span3",
            "parentSpanId": "span1",
            "serviceName": "order-service",
            "operationName": "getOrders",
            "startTime": 1621500000120,
            "endTime": 1621500000320,
            "duration": 200,
            "tags": {
                "userId": "user123",
                "http.method": "GET",
                "http.url": "http://example.com/api/orders",
                "http.status_code": "200"
            }
        }
    ]
}
```

#### 测试用例2：错误追踪
```
// 输入：模拟包含错误的HTTP服务调用
Tracer.startSpan("http-client", "executeRequest", null);
    Tracer.addTag("requestId", "12346");
    Tracer.addTag("userId", "user456");
    Tracer.addTag("http.method", "GET");
    Tracer.addTag("http.url", "http://example.com/api/user");
    
    // 模拟HTTP调用用户服务
    String parentSpan = Tracer.getCurrentSpanId();
    Tracer.startSpan("user-service", "getUserInfo", parentSpan);
        Tracer.addTag("userId", "user456");
        Tracer.addTag("http.status_code", "200");
        // 模拟处理逻辑
        Thread.sleep(50);
    Tracer.finishSpan();
    
    // 模拟HTTP调用订单服务出错
    Tracer.startSpan("order-service", "getOrders", parentSpan);
        Tracer.addTag("userId", "user456");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders");
        // 模拟错误
        Tracer.addTag("error", "true");
        Tracer.addTag("error.message", "Database connection failed");
        Tracer.addTag("http.status_code", "500");
        Thread.sleep(100);
    Tracer.finishSpan();
    
    // 模拟错误处理
    Tracer.startSpan("error-handler", "handleError", parentSpan);
        Tracer.addTag("error.service", "order-service");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(30);
    Tracer.finishSpan();
    
Tracer.finishSpan();

// 期望输出：包含错误信息的调用链
{
    "traceId": "trace456",
    "spans": [
        {
            "spanId": "span1",
            "parentSpanId": null,
            "serviceName": "http-client",
            "operationName": "executeRequest",
            "startTime": 1621500100000,
            "endTime": 1621500100230,
            "duration": 230,
            "tags": {
                "requestId": "12346",
                "userId": "user456",
                "http.method": "GET",
                "http.url": "http://example.com/api/user"
            }
        },
        {
            "spanId": "span2",
            "parentSpanId": "span1",
            "serviceName": "user-service",
            "operationName": "getUserInfo",
            "startTime": 1621500100010,
            "endTime": 1621500100060,
            "duration": 50,
            "tags": {
                "userId": "user456",
                "http.status_code": "200"
            }
        },
        {
            "spanId": "span3",
            "parentSpanId": "span1",
            "serviceName": "order-service",
            "operationName": "getOrders",
            "startTime": 1621500100070,
            "endTime": 1621500100170,
            "duration": 100,
            "tags": {
                "userId": "user456",
                "http.method": "GET",
                "http.url": "http://example.com/api/orders",
                "error": "true",
                "error.message": "Database connection failed",
                "http.status_code": "500"
            },
            "error": true
        },
        {
            "spanId": "span4",
            "parentSpanId": "span1",
            "serviceName": "error-handler",
            "operationName": "handleError",
            "startTime": 1621500100180,
            "endTime": 1621500100210,
            "duration": 30,
            "tags": {
                "error.service": "order-service",
                "http.status_code": "200"
            }
        }
    ]
}
```

#### 测试用例3：性能分析
```
// 输入：查询特定时间范围内的调用链
List<TraceContext> traces = traceStorage.queryByTimeRange(1621500000000L, 1621500200000L);

// 期望输出：时间范围内的调用链统计信息
{
    "timeRange": {
        "start": 1621500000000,
        "end": 1621500200000
    },
    "totalTraces": 2,
    "avgDuration": 290,
    "maxDuration": 350,
    "minDuration": 230,
    "serviceStats": [
        {
            "serviceName": "api-gateway",
            "callCount": 2,
            "avgDuration": 290,
            "errorCount": 0
        },
        {
            "serviceName": "user-service",
            "callCount": 2,
            "avgDuration": 75,
            "errorCount": 0
        },
        {
            "serviceName": "order-service",
            "callCount": 2,
            "avgDuration": 150,
            "errorCount": 1
        },
        {
            "serviceName": "error-handler",
            "callCount": 1,
            "avgDuration": 30,
            "errorCount": 0
        }
    ],
    "errorTraces": 1
}
```

#### 测试用例4：调用链可视化
```
// 输入：请求特定traceId的可视化数据
String visualData = traceVisualizer.generateVisualData("trace123");

// 期望输出：可用于前端展示的调用链数据
{
    "nodes": [
        {
            "id": "span1",
            "label": "api-gateway\nhandleRequest",
            "duration": 350,
            "status": "success"
        },
        {
            "id": "span2",
            "label": "user-service\ngetUserInfo",
            "duration": 100,
            "status": "success"
        },
        {
            "id": "span3",
            "label": "order-service\ngetOrders",
            "duration": 200,
            "status": "success"
        }
    ],
    "edges": [
        {
            "from": "span1",
            "to": "span2",
            "label": "调用"
        },
        {
            "from": "span1",
            "to": "span3",
            "label": "调用"
        }
    ],
    "timeline": [
        {
            "id": "span1",
            "start": 1621500000000,
            "end": 1621500000350
        },
        {
            "id": "span2",
            "start": 1621500000010,
            "end": 1621500000110
        },
        {
            "id": "span3",
            "start": 1621500000120,
            "end": 1621500000320
        }
    ]
}
```

#### 测试用例5：分布式追踪集成
```
// 输入：模拟微服务架构中的复杂HTTP调用链
Tracer.startSpan("web-browser", "pageLoad", null);
    Tracer.addTag("requestId", "req789");
    Tracer.addTag("clientIp", "*************");
    Tracer.addTag("http.url", "http://example.com/dashboard");
    
    String browserSpan = Tracer.getCurrentSpanId();
    
    // 模拟HTTP调用认证服务
    Tracer.startSpan("auth-service", "authenticate", browserSpan);
        Tracer.addTag("authType", "jwt");
        Tracer.addTag("http.method", "POST");
        Tracer.addTag("http.url", "http://example.com/api/auth");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(50);
    Tracer.finishSpan();
    
    // 模拟HTTP调用用户服务
    Tracer.startSpan("user-service", "getUserProfile", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/users/789");
        Tracer.addTag("http.status_code", "200");
        
        String userSpan = Tracer.getCurrentSpanId();
        
        // 模拟用户服务HTTP调用缓存服务
        Tracer.startSpan("cache-service", "getFromCache", userSpan);
            Tracer.addTag("cacheKey", "user:789:profile");
            Tracer.addTag("http.method", "GET");
            Tracer.addTag("http.url", "http://example.com/api/cache");
            Tracer.addTag("http.status_code", "200");
            Thread.sleep(20);
            // 模拟缓存未命中
            Tracer.addTag("cacheHit", "false");
        Tracer.finishSpan();
        
        // 模拟用户服务HTTP调用数据库服务
        Tracer.startSpan("db-service", "queryUserData", userSpan);
            Tracer.addTag("table", "users");
            Tracer.addTag("query", "SELECT * FROM users WHERE id = 789");
            Tracer.addTag("http.method", "POST");
            Tracer.addTag("http.url", "http://example.com/api/db/query");
            Tracer.addTag("http.status_code", "200");
            Thread.sleep(100);
        Tracer.finishSpan();
        
        Thread.sleep(30);
    Tracer.finishSpan();
    
    // 模拟HTTP调用订单服务
    Tracer.startSpan("order-service", "getRecentOrders", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("limit", "5");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/orders?userId=789&limit=5");
        Tracer.addTag("http.status_code", "200");
        
        String orderSpan = Tracer.getCurrentSpanId();
        
        // 模拟订单服务HTTP调用数据库服务
        Tracer.startSpan("db-service", "queryOrderData", orderSpan);
            Tracer.addTag("table", "orders");
            Tracer.addTag("query", "SELECT * FROM orders WHERE user_id = 789 ORDER BY created_at DESC LIMIT 5");
            Tracer.addTag("http.method", "POST");
            Tracer.addTag("http.url", "http://example.com/api/db/query");
            Tracer.addTag("http.status_code", "200");
            Thread.sleep(120);
        Tracer.finishSpan();
        
        Thread.sleep(30);
    Tracer.finishSpan();
    
    // 模拟HTTP调用推荐服务
    Tracer.startSpan("recommendation-service", "getRecommendations", browserSpan);
        Tracer.addTag("userId", "user789");
        Tracer.addTag("category", "recent_viewed");
        Tracer.addTag("http.method", "GET");
        Tracer.addTag("http.url", "http://example.com/api/recommendations?userId=789&category=recent_viewed");
        Tracer.addTag("http.status_code", "200");
        Thread.sleep(150);
    Tracer.finishSpan();
    
    Thread.sleep(20);
Tracer.finishSpan();

// 期望输出：复杂调用链的可视化数据
{
    "traceId": "trace789",
    "rootSpan": "span1",
    "services": ["web-browser", "auth-service", "user-service", "cache-service", "db-service", "order-service", "recommendation-service"],
    "totalDuration": 520,
    "spanCount": 8,
    "criticalPath": ["web-browser", "recommendation-service"],
    "bottlenecks": [
        {
            "service": "recommendation-service",
            "duration": 150,
            "percentage": 28.8
        },
        {
            "service": "db-service (queryOrderData)",
            "duration": 120,
            "percentage": 23.1
        }
    ],
    "visualization": "[复杂调用链的可视化数据结构]"
}
```